/**
 * 设备订单排序服务
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 17:30:00 +08:00; Reason: Shrimp Task ID: #e99ef4f8-c506-484b-8489-79f62fd753e3, 创建设备排产业务服务层; Principle_Applied: 业务逻辑分离;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 13:40:00 +08:00; Reason: Shrimp Task ID: #7d694af5-1e7b-4c38-96e4-7cbb7be62713, 扩展业务逻辑添加生产管理功能; Principle_Applied: 业务逻辑扩展;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 14:30:00 +08:00; Reason: 修复订单状态同步问题, 添加syncOrderStatus方法; Principle_Applied: 数据一致性;}}
 */

import { Op, WhereOptions, Transaction } from 'sequelize';
import { DeviceOrderSequence, DeviceOrderSequenceStatus } from '../../shared/database/models/DeviceOrderSequence';
import { Order, OrderStatus } from '../../shared/database/models/Order';
import { Device } from '../../shared/database/models/Device';
import { Pattern } from '../../shared/database/models/Pattern';
import { Tag, TagType } from '../../shared/database/models/Tag';
import { ProductionHistory, ProductionHistoryActionType } from '../../shared/database/models/ProductionHistory';
import { User } from '../../shared/database/models/User';
import { sequelize } from '../../shared/database';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

// 创建排产请求
export interface CreateSequenceRequest {
  enterpriseId: number;
  deviceId: number;
  orderId: number;
  patternId: number;
  formula?: string;
  lathesNum?: number;
  productionSequence?: number;
}

// 更新排产请求
export interface UpdateSequenceRequest {
  formula?: string;
  lathesNum?: number;
  productionSequence?: number;
  productionQuantity?: number;
  status?: DeviceOrderSequenceStatus;
}

// 排产列表查询参数
export interface SequenceListQuery {
  page?: number;
  pageSize?: number;
  deviceId?: number;
  orderId?: number;
  patternId?: number;
  status?: DeviceOrderSequenceStatus;
  enterpriseId: number;
}

// 排产列表响应
export interface SequenceListResponse {
  sequences: DeviceOrderSequence[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 重新排序请求
export interface ReorderSequencesRequest {
  deviceId: number;
  sequenceIds: number[];
  enterpriseId: number;
}

// 生产数量计算结果
export interface ProductionQuantityResult {
  productionQuantity: number;
  formula: string;
  lathesNum: number;
  deviceHeadNum: number;
}

// 开始生产请求
export interface StartProductionRequest {
  sequenceId: number;
  enterpriseId: number;
  operatorId?: number;
  remark?: string;
}

// 完成生产请求
export interface CompleteProductionRequest {
  sequenceId: number;
  enterpriseId: number;
  operatorId?: number;
  actualQuantity?: number;
  remark?: string;
}

// 更新进度请求
export interface UpdateProgressRequest {
  sequenceId: number;
  enterpriseId: number;
  progress: number;
  operatorId?: number;
  remark?: string;
}

// 生产历史查询参数
export interface ProductionHistoryQuery {
  sequenceId?: number;
  enterpriseId: number;
  actionType?: ProductionHistoryActionType;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}

// 生产统计数据
export interface ProductionStatistics {
  totalSequences: number;
  completedSequences: number;
  inProgressSequences: number;
  waitingSequences: number;
  completionRate: number;
  averageProductionTime: number;
  deviceUtilization: Array<{
    deviceId: number;
    deviceName: string;
    utilizationRate: number;
    totalSequences: number;
    completedSequences: number;
  }>;
  dailyStats: Array<{
    date: string;
    completedCount: number;
    averageTime: number;
  }>;
}

// 批量创建排产请求
export interface BatchCreateSequenceRequest {
  enterpriseId: number;
  orderId: number;
  allocations: Array<{
    deviceId: number;
    patternId: number;
    lathesNum: number;
    formula?: string;
  }>;
}

export class DeviceOrderSequenceService {
  /**
   * 创建排产记录
   */
  async createSequence(data: CreateSequenceRequest, transaction?: Transaction): Promise<DeviceOrderSequence> {
    const tx = transaction || await sequelize.transaction();
    const shouldCommit = !transaction;

    try {
      // 验证订单是否存在且属于当前企业
      const order = await Order.findOne({
        where: { id: data.orderId, enterpriseId: data.enterpriseId },
        transaction: tx
      });

      if (!order) {
        throw createApiError('订单不存在', 404, 'ORDER_NOT_FOUND');
      }

      // 验证设备是否存在且属于当前企业
      const device = await Device.findOne({
        where: { id: data.deviceId, enterpriseId: data.enterpriseId },
        transaction: tx
      });

      if (!device) {
        throw createApiError('设备不存在', 404, 'DEVICE_NOT_FOUND');
      }

      // 验证花样是否存在且属于当前企业
      const pattern = await Pattern.findOne({
        where: { id: data.patternId, enterpriseId: data.enterpriseId },
        transaction: tx
      });

      if (!pattern) {
        throw createApiError('花样不存在', 404, 'PATTERN_NOT_FOUND');
      }

      // 检查是否已存在相同的排产记录
      const existingSequence = await DeviceOrderSequence.findOne({
        where: {
          deviceId: data.deviceId,
          orderId: data.orderId,
          patternId: data.patternId,
          enterpriseId: data.enterpriseId
        },
        transaction: tx
      });

      if (existingSequence) {
        throw createApiError('该设备上已存在相同的排产记录', 400, 'SEQUENCE_ALREADY_EXISTS');
      }

      // 获取下一个排序号
      let productionSequence = data.productionSequence;
      if (!productionSequence) {
        const maxSequence = await DeviceOrderSequence.max('productionSequence', {
          where: { deviceId: data.deviceId, enterpriseId: data.enterpriseId },
          transaction: tx
        }) as number;
        productionSequence = (maxSequence || 0) + 1;
      }

      // 获取订单单位名称，生成默认公式
      let defaultFormula = '1件=1头'; // 默认值
      if (order.orderUnitId) {
        const unitTag = await Tag.findOne({
          where: {
            id: order.orderUnitId,
            enterpriseId: data.enterpriseId,
            type: TagType.PATTERN_UNIT
          },
          transaction: tx
        });
        if (unitTag) {
          defaultFormula = `1${unitTag.name}=1头`;
        }
      }

      // 计算生产数量
      const formula = data.formula || defaultFormula;
      const lathesNum = data.lathesNum || 0;
      const deviceHeadNum = device.headNum || 1;
      const productionQuantity = this.calculateProductionQuantity(lathesNum, deviceHeadNum, formula);

      // 创建排产记录
      const sequence = await DeviceOrderSequence.create({
        ...data,
        formula,
        productionSequence,
        productionQuantity,
        status: DeviceOrderSequenceStatus.WAITING
      }, { transaction: tx });

      // 同步订单状态
      await this.syncOrderStatus(data.orderId, data.enterpriseId, tx);

      if (shouldCommit) {
        await tx.commit();
      }

      logger.info('创建排产记录成功', {
        sequenceId: sequence.id,
        deviceId: data.deviceId,
        orderId: data.orderId,
        patternId: data.patternId,
        enterpriseId: data.enterpriseId
      });

      return sequence;
    } catch (error) {
      if (shouldCommit) {
        await tx.rollback();
      }
      logger.error('创建排产记录失败', {
        data,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 批量创建排产记录
   */
  async batchCreateSequences(data: BatchCreateSequenceRequest): Promise<DeviceOrderSequence[]> {
    const transaction = await sequelize.transaction();

    try {
      // 验证订单是否存在且属于当前企业
      const order = await Order.findOne({
        where: { id: data.orderId, enterpriseId: data.enterpriseId },
        transaction
      });

      if (!order) {
        throw createApiError('订单不存在', 404, 'ORDER_NOT_FOUND');
      }

      // 验证所有设备是否存在且属于当前企业
      const deviceIds = data.allocations.map(a => a.deviceId);
      const devices = await Device.findAll({
        where: {
          id: { [Op.in]: deviceIds },
          enterpriseId: data.enterpriseId
        },
        transaction
      });

      if (devices.length !== deviceIds.length) {
        throw createApiError('部分设备不存在', 400, 'INVALID_DEVICES');
      }

      // 验证所有花样是否存在且属于当前企业
      const patternIds = data.allocations.map(a => a.patternId);
      const patterns = await Pattern.findAll({
        where: {
          id: { [Op.in]: patternIds },
          enterpriseId: data.enterpriseId
        },
        transaction
      });

      if (patterns.length !== patternIds.length) {
        throw createApiError('部分花样不存在', 400, 'INVALID_PATTERNS');
      }

      // 检查是否存在重复的设备分配
      const deviceIdSet = new Set(deviceIds);
      if (deviceIdSet.size !== deviceIds.length) {
        throw createApiError('不能重复选择同一设备', 400, 'DUPLICATE_DEVICES');
      }

      // 批量创建排产记录
      const sequences: DeviceOrderSequence[] = [];
      for (const allocation of data.allocations) {
        const sequence = await this.createSequence({
          ...allocation,
          orderId: data.orderId,
          enterpriseId: data.enterpriseId
        }, transaction);
        sequences.push(sequence);
      }

      await transaction.commit();

      logger.info('批量创建排产记录成功', {
        orderId: data.orderId,
        enterpriseId: data.enterpriseId,
        sequenceCount: sequences.length,
        sequenceIds: sequences.map(s => s.id)
      });

      return sequences;
    } catch (error) {
      await transaction.rollback();
      logger.error('批量创建排产记录失败', {
        data,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 计算生产数量
   */
  private calculateProductionQuantity(lathesNum: number, deviceHeadNum: number, formula: string): number {
    if (!lathesNum || !deviceHeadNum) return 0;

    // 解析计算公式，支持任何中文单位，如 "1件=1头", "1套=1头" 等
    const match = formula.match(/(\d+)[\u4e00-\u9fff]+=(\d+)头/);

    if (match) {
      const pieces = parseInt(match[1]);
      const heads = parseInt(match[2]);
      return Math.floor((lathesNum * deviceHeadNum * pieces) / heads);
    }

    // 默认计算方式：车数 * 设备头数
    return lathesNum * deviceHeadNum;
  }

  /**
   * 获取生产数量计算详情
   */
  async getProductionQuantityCalculation(
    lathesNum: number, 
    deviceId: number, 
    formula: string, 
    enterpriseId: number
  ): Promise<ProductionQuantityResult> {
    try {
      const device = await Device.findOne({
        where: { id: deviceId, enterpriseId },
        attributes: ['headNum']
      });

      if (!device) {
        throw createApiError('设备不存在', 404, 'DEVICE_NOT_FOUND');
      }

      const deviceHeadNum = device.headNum || 1;
      const productionQuantity = this.calculateProductionQuantity(lathesNum, deviceHeadNum, formula);

      return {
        productionQuantity,
        formula,
        lathesNum,
        deviceHeadNum
      };
    } catch (error) {
      logger.error('计算生产数量失败', {
        lathesNum,
        deviceId,
        formula,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新排产记录
   */
  async updateSequence(id: number, data: UpdateSequenceRequest, enterpriseId: number): Promise<DeviceOrderSequence> {
    const transaction = await sequelize.transaction();

    try {
      const sequence = await DeviceOrderSequence.findOne({
        where: { id, enterpriseId },
        include: [
          {
            association: 'device',
            attributes: ['headNum']
          }
        ],
        transaction
      });

      if (!sequence) {
        throw createApiError('排产记录不存在', 404, 'SEQUENCE_NOT_FOUND');
      }

      // 检查是否可以编辑
      if (!sequence.canEdit() && data.status === undefined) {
        throw createApiError('当前状态下不允许编辑排产记录', 400, 'SEQUENCE_CANNOT_EDIT');
      }

      // 如果更新了车数或公式，重新计算生产数量
      let updateData = { ...data };
      if (data.lathesNum !== undefined || data.formula !== undefined) {
        const lathesNum = data.lathesNum ?? sequence.lathesNum ?? 0;
        const formula = data.formula ?? sequence.formula ?? '1件=1头';
        const deviceHeadNum = sequence.device?.headNum || 1;

        updateData.productionQuantity = this.calculateProductionQuantity(lathesNum, deviceHeadNum, formula);
      }

      // 如果更新排序，检查排序冲突
      if (data.productionSequence !== undefined && data.productionSequence !== sequence.productionSequence) {
        const conflictSequence = await DeviceOrderSequence.findOne({
          where: {
            deviceId: sequence.deviceId,
            productionSequence: data.productionSequence,
            enterpriseId,
            id: { [Op.ne]: id }
          },
          transaction
        });

        if (conflictSequence) {
          throw createApiError('排序号已被占用', 400, 'SEQUENCE_ORDER_CONFLICT');
        }
      }

      // 更新排产记录
      await sequence.update(updateData, { transaction });

      await transaction.commit();

      logger.info('更新排产记录成功', {
        sequenceId: id,
        enterpriseId
      });

      return sequence;
    } catch (error) {
      await transaction.rollback();
      logger.error('更新排产记录失败', {
        sequenceId: id,
        data,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除排产记录
   */
  async deleteSequence(id: number, enterpriseId: number): Promise<void> {
    const transaction = await sequelize.transaction();

    try {
      const sequence = await DeviceOrderSequence.findOne({
        where: { id, enterpriseId },
        transaction
      });

      if (!sequence) {
        throw createApiError('排产记录不存在', 404, 'SEQUENCE_NOT_FOUND');
      }

      // 检查是否可以删除
      if (!sequence.canDelete()) {
        throw createApiError('当前状态下不允许删除排产记录', 400, 'SEQUENCE_CANNOT_DELETE');
      }

      const orderId = sequence.orderId;

      // 删除排产记录
      await sequence.destroy({ transaction });

      // 同步订单状态
      await this.syncOrderStatus(orderId, enterpriseId, transaction);

      await transaction.commit();

      logger.info('删除排产记录成功', {
        sequenceId: id,
        enterpriseId
      });
    } catch (error) {
      await transaction.rollback();
      logger.error('删除排产记录失败', {
        sequenceId: id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 根据订单获取排产记录
   */
  async getSequencesByOrder(orderId: number, enterpriseId: number): Promise<DeviceOrderSequence[]> {
    try {
      const sequences = await DeviceOrderSequence.findAll({
        where: { orderId, enterpriseId },
        include: [
          {
            association: 'device',
            attributes: ['id', 'name', 'code', 'headNum']
          },
          {
            association: 'pattern',
            attributes: ['id', 'name', 'code']
          }
        ],
        order: [['productionSequence', 'ASC']]
      });

      logger.info('获取订单排产记录成功', {
        orderId,
        count: sequences.length,
        enterpriseId
      });

      return sequences;
    } catch (error) {
      logger.error('获取订单排产记录失败', {
        orderId,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 根据设备获取排产队列
   */
  async getSequencesByDevice(deviceId: number, enterpriseId: number): Promise<DeviceOrderSequence[]> {
    try {
      const sequences = await DeviceOrderSequence.findAll({
        where: { deviceId, enterpriseId },
        include: [
          {
            association: 'order',
            attributes: ['id', 'code', 'customerName', 'deliveryDate']
          },
          {
            association: 'pattern',
            attributes: ['id', 'name', 'code']
          }
        ],
        order: [['productionSequence', 'ASC']]
      });

      logger.info('获取设备排产队列成功', {
        deviceId,
        count: sequences.length,
        enterpriseId
      });

      return sequences;
    } catch (error) {
      logger.error('获取设备排产队列失败', {
        deviceId,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 重新排序排产记录
   */
  async reorderSequences(data: ReorderSequencesRequest): Promise<void> {
    const transaction = await sequelize.transaction();

    try {
      const { deviceId, sequenceIds, enterpriseId } = data;

      // 验证所有排产记录都存在且属于指定设备和企业
      const sequences = await DeviceOrderSequence.findAll({
        where: {
          id: { [Op.in]: sequenceIds },
          deviceId,
          enterpriseId
        },
        transaction
      });

      if (sequences.length !== sequenceIds.length) {
        throw createApiError('部分排产记录不存在或不属于指定设备', 400, 'INVALID_SEQUENCE_IDS');
      }

      // 检查所有排产记录是否都可以编辑
      const cannotEditSequences = sequences.filter(seq => !seq.canEdit());
      if (cannotEditSequences.length > 0) {
        throw createApiError('部分排产记录当前状态下不允许调整排序', 400, 'SEQUENCES_CANNOT_REORDER');
      }

      // 批量更新排序
      const updatePromises = sequenceIds.map((sequenceId, index) => {
        return DeviceOrderSequence.update(
          { productionSequence: index + 1 },
          {
            where: { id: sequenceId, enterpriseId },
            transaction
          }
        );
      });

      await Promise.all(updatePromises);

      await transaction.commit();

      logger.info('重新排序排产记录成功', {
        deviceId,
        sequenceCount: sequenceIds.length,
        enterpriseId
      });
    } catch (error) {
      await transaction.rollback();
      logger.error('重新排序排产记录失败', {
        data,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取排产记录列表
   */
  async getSequenceList(query: SequenceListQuery): Promise<SequenceListResponse> {
    try {
      const {
        page = 1,
        pageSize = 10,
        deviceId,
        orderId,
        patternId,
        status,
        enterpriseId
      } = query;

      // 构建查询条件
      const whereConditions: WhereOptions = {
        enterpriseId
      };

      if (deviceId) {
        whereConditions.deviceId = deviceId;
      }
      if (orderId) {
        whereConditions.orderId = orderId;
      }
      if (patternId) {
        whereConditions.patternId = patternId;
      }
      if (status !== undefined) {
        whereConditions.status = status;
      }

      // 计算偏移量
      const offset = (page - 1) * pageSize;

      // 查询排产记录列表
      const { rows: sequences, count: total } = await DeviceOrderSequence.findAndCountAll({
        where: whereConditions,
        include: [
          {
            association: 'device',
            attributes: ['id', 'name', 'code', 'headNum']
          },
          {
            association: 'order',
            attributes: ['id', 'code', 'customerName', 'deliveryDate']
          },
          {
            association: 'pattern',
            attributes: ['id', 'name', 'code']
          }
        ],
        order: [['deviceId', 'ASC'], ['productionSequence', 'ASC']],
        limit: pageSize,
        offset,
        distinct: true
      });

      const totalPages = Math.ceil(total / pageSize);

      logger.info('获取排产记录列表成功', {
        total,
        page,
        pageSize,
        enterpriseId
      });

      return {
        sequences,
        total,
        page,
        pageSize,
        totalPages
      };
    } catch (error) {
      logger.error('获取排产记录列表失败', {
        query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 开始生产
   */
  async startProduction(data: StartProductionRequest): Promise<DeviceOrderSequence> {
    const transaction = await sequelize.transaction();

    try {
      const sequence = await DeviceOrderSequence.findOne({
        where: { id: data.sequenceId, enterpriseId: data.enterpriseId },
        include: [
          {
            association: 'order',
            attributes: ['id', 'status']
          }
        ],
        transaction
      });

      if (!sequence) {
        throw createApiError('排产记录不存在', 404, 'SEQUENCE_NOT_FOUND');
      }

      if (!sequence.canStart()) {
        throw createApiError('当前状态下不允许开始生产', 400, 'CANNOT_START_PRODUCTION');
      }

      // 更新排产记录状态和开始时间
      await sequence.update({
        status: DeviceOrderSequenceStatus.IN_PRODUCTION,
        actualStartTime: new Date(),
        progress: 0
      }, { transaction });

      // 记录生产历史
      await this.createProductionHistory({
        enterpriseId: data.enterpriseId,
        sequenceId: data.sequenceId,
        actionType: ProductionHistoryActionType.START,
        actionTime: new Date(),
        operatorId: data.operatorId,
        remark: data.remark || '开始生产'
      }, transaction);

      // 同步订单状态
      await this.syncOrderStatus(sequence.orderId, data.enterpriseId, transaction);

      await transaction.commit();

      logger.info('开始生产成功', {
        sequenceId: data.sequenceId,
        enterpriseId: data.enterpriseId,
        operatorId: data.operatorId
      });

      return sequence.reload();
    } catch (error) {
      await transaction.rollback();
      logger.error('开始生产失败', {
        data,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 完成生产
   */
  async completeProduction(data: CompleteProductionRequest): Promise<DeviceOrderSequence> {
    const transaction = await sequelize.transaction();

    try {
      const sequence = await DeviceOrderSequence.findOne({
        where: { id: data.sequenceId, enterpriseId: data.enterpriseId },
        include: [
          {
            association: 'order',
            attributes: ['id', 'status']
          }
        ],
        transaction
      });

      if (!sequence) {
        throw createApiError('排产记录不存在', 404, 'SEQUENCE_NOT_FOUND');
      }

      if (!sequence.canComplete()) {
        throw createApiError('当前状态下不允许完成生产', 400, 'CANNOT_COMPLETE_PRODUCTION');
      }

      // 更新排产记录状态和完成时间
      const updateData: any = {
        status: DeviceOrderSequenceStatus.COMPLETED,
        actualEndTime: new Date(),
        progress: 100
      };

      if (data.actualQuantity !== undefined) {
        updateData.actualQuantity = data.actualQuantity;
      }

      await sequence.update(updateData, { transaction });

      // 记录生产历史
      await this.createProductionHistory({
        enterpriseId: data.enterpriseId,
        sequenceId: data.sequenceId,
        actionType: ProductionHistoryActionType.COMPLETE,
        actionTime: new Date(),
        operatorId: data.operatorId,
        newValue: data.actualQuantity?.toString(),
        remark: data.remark || '完成生产'
      }, transaction);

      // 同步订单状态
      await this.syncOrderStatus(sequence.orderId, data.enterpriseId, transaction);

      await transaction.commit();

      logger.info('完成生产成功', {
        sequenceId: data.sequenceId,
        enterpriseId: data.enterpriseId,
        operatorId: data.operatorId,
        actualQuantity: data.actualQuantity
      });

      return sequence.reload();
    } catch (error) {
      await transaction.rollback();
      logger.error('完成生产失败', {
        data,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新生产进度
   */
  async updateProgress(data: UpdateProgressRequest): Promise<DeviceOrderSequence> {
    const transaction = await sequelize.transaction();

    try {
      const sequence = await DeviceOrderSequence.findOne({
        where: { id: data.sequenceId, enterpriseId: data.enterpriseId },
        transaction
      });

      if (!sequence) {
        throw createApiError('排产记录不存在', 404, 'SEQUENCE_NOT_FOUND');
      }

      if (!sequence.canUpdateProgress()) {
        throw createApiError('当前状态下不允许更新进度', 400, 'CANNOT_UPDATE_PROGRESS');
      }

      const previousProgress = sequence.progress || 0;

      // 更新进度
      await sequence.update({
        progress: data.progress
      }, { transaction });

      // 记录生产历史
      await this.createProductionHistory({
        enterpriseId: data.enterpriseId,
        sequenceId: data.sequenceId,
        actionType: ProductionHistoryActionType.PROGRESS_UPDATE,
        actionTime: new Date(),
        operatorId: data.operatorId,
        previousValue: `${previousProgress}%`,
        newValue: `${data.progress}%`,
        remark: data.remark || `进度更新：${previousProgress}% → ${data.progress}%`
      }, transaction);

      await transaction.commit();

      logger.info('更新生产进度成功', {
        sequenceId: data.sequenceId,
        enterpriseId: data.enterpriseId,
        progress: data.progress,
        operatorId: data.operatorId
      });

      return sequence.reload();
    } catch (error) {
      await transaction.rollback();
      logger.error('更新生产进度失败', {
        data,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 同步订单状态
   * 根据订单的所有排产记录状态来更新订单状态
   */
  private async syncOrderStatus(
    orderId: number,
    enterpriseId: number,
    transaction?: Transaction
  ): Promise<void> {
    try {
      // 获取订单的所有排产记录
      const sequences = await DeviceOrderSequence.findAll({
        where: { orderId, enterpriseId },
        transaction
      });

      if (sequences.length === 0) {
        // 如果没有排产记录，订单状态应该是未排产
        await Order.update(
          { status: OrderStatus.UNSCHEDULED },
          { where: { id: orderId, enterpriseId }, transaction }
        );
        return;
      }

      // 检查所有排产记录的状态
      const hasInProduction = sequences.some(seq => seq.status === DeviceOrderSequenceStatus.IN_PRODUCTION);
      const allCompleted = sequences.every(seq => seq.status === DeviceOrderSequenceStatus.COMPLETED);

      let newOrderStatus: OrderStatus;
      if (allCompleted) {
        newOrderStatus = OrderStatus.COMPLETED;
      } else if (hasInProduction) {
        newOrderStatus = OrderStatus.IN_PRODUCTION;
      } else {
        newOrderStatus = OrderStatus.SCHEDULED;
      }

      // 更新订单状态
      await Order.update(
        { status: newOrderStatus },
        { where: { id: orderId, enterpriseId }, transaction }
      );

      logger.info('订单状态同步成功', {
        orderId,
        enterpriseId,
        newStatus: newOrderStatus,
        sequenceCount: sequences.length
      });
    } catch (error) {
      logger.error('订单状态同步失败', {
        orderId,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建生产历史记录
   */
  private async createProductionHistory(
    data: {
      enterpriseId: number;
      sequenceId: number;
      actionType: ProductionHistoryActionType;
      actionTime: Date;
      operatorId?: number;
      previousValue?: string;
      newValue?: string;
      remark?: string;
    },
    transaction?: Transaction
  ): Promise<ProductionHistory> {
    try {
      const history = await ProductionHistory.create(data, { transaction });

      logger.info('创建生产历史记录成功', {
        historyId: history.id,
        sequenceId: data.sequenceId,
        actionType: data.actionType,
        enterpriseId: data.enterpriseId
      });

      return history;
    } catch (error) {
      logger.error('创建生产历史记录失败', {
        data,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取生产历史记录
   */
  async getProductionHistory(query: ProductionHistoryQuery): Promise<{
    histories: ProductionHistory[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const {
        sequenceId,
        enterpriseId,
        actionType,
        startDate,
        endDate,
        page = 1,
        pageSize = 20
      } = query;

      const whereConditions: WhereOptions = {
        enterpriseId
      };

      if (sequenceId) {
        whereConditions.sequenceId = sequenceId;
      }

      if (actionType) {
        whereConditions.actionType = actionType;
      }

      // 时间范围筛选
      if (startDate || endDate) {
        const dateConditions: any = {};
        if (startDate) {
          dateConditions[Op.gte] = new Date(startDate);
        }
        if (endDate) {
          dateConditions[Op.lte] = new Date(endDate);
        }
        whereConditions.actionTime = dateConditions;
      }

      const offset = (page - 1) * pageSize;

      const { rows: histories, count: total } = await ProductionHistory.findAndCountAll({
        where: whereConditions,
        include: [
          {
            association: 'sequence',
            attributes: ['id', 'deviceId', 'orderId', 'patternId'],
            include: [
              {
                association: 'device',
                attributes: ['id', 'name', 'code']
              },
              {
                association: 'order',
                attributes: ['id', 'code', 'customerName']
              },
              {
                association: 'pattern',
                attributes: ['id', 'name', 'code']
              }
            ]
          },
          {
            association: 'operator',
            attributes: ['id', 'realName', 'username']
          }
        ],
        order: [['actionTime', 'DESC']],
        limit: pageSize,
        offset,
        distinct: true
      });

      logger.info('获取生产历史记录成功', {
        total,
        page,
        pageSize,
        enterpriseId
      });

      return {
        histories,
        total,
        page,
        pageSize
      };
    } catch (error) {
      logger.error('获取生产历史记录失败', {
        query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取生产统计数据
   */
  async getProductionStatistics(
    enterpriseId: number,
    startDate?: string,
    endDate?: string
  ): Promise<ProductionStatistics> {
    try {
      const whereConditions: WhereOptions = {
        enterpriseId
      };

      // 时间范围筛选
      if (startDate || endDate) {
        const dateConditions: any = {};
        if (startDate) {
          dateConditions[Op.gte] = new Date(startDate);
        }
        if (endDate) {
          dateConditions[Op.lte] = new Date(endDate);
        }
        whereConditions.createdAt = dateConditions;
      }

      // 获取基础统计数据
      const sequences = await DeviceOrderSequence.findAll({
        where: whereConditions,
        include: [
          {
            association: 'device',
            attributes: ['id', 'name']
          }
        ],
        attributes: [
          'id', 'deviceId', 'status', 'actualStartTime', 'actualEndTime', 'createdAt'
        ]
      });

      const totalSequences = sequences.length;
      const completedSequences = sequences.filter(s => s.status === DeviceOrderSequenceStatus.COMPLETED).length;
      const inProgressSequences = sequences.filter(s => s.status === DeviceOrderSequenceStatus.IN_PRODUCTION).length;
      const waitingSequences = sequences.filter(s => s.status === DeviceOrderSequenceStatus.WAITING).length;
      const completionRate = totalSequences > 0 ? (completedSequences / totalSequences) * 100 : 0;

      // 计算平均生产时间
      const completedWithTime = sequences.filter(s =>
        s.status === DeviceOrderSequenceStatus.COMPLETED &&
        s.actualStartTime &&
        s.actualEndTime
      );

      let averageProductionTime = 0;
      if (completedWithTime.length > 0) {
        const totalTime = completedWithTime.reduce((sum, s) => {
          const duration = s.actualEndTime!.getTime() - s.actualStartTime!.getTime();
          return sum + duration;
        }, 0);
        averageProductionTime = Math.floor(totalTime / completedWithTime.length / (1000 * 60)); // 转换为分钟
      }

      // 设备利用率统计
      const deviceStats = new Map<number, {
        deviceName: string;
        totalSequences: number;
        completedSequences: number;
      }>();

      sequences.forEach(sequence => {
        const deviceId = sequence.deviceId;
        const deviceName = sequence.device?.name || `设备${deviceId}`;

        if (!deviceStats.has(deviceId)) {
          deviceStats.set(deviceId, {
            deviceName,
            totalSequences: 0,
            completedSequences: 0
          });
        }

        const stats = deviceStats.get(deviceId)!;
        stats.totalSequences++;
        if (sequence.status === DeviceOrderSequenceStatus.COMPLETED) {
          stats.completedSequences++;
        }
      });

      const deviceUtilization = Array.from(deviceStats.entries()).map(([deviceId, stats]) => ({
        deviceId,
        deviceName: stats.deviceName,
        utilizationRate: stats.totalSequences > 0 ? (stats.completedSequences / stats.totalSequences) * 100 : 0,
        totalSequences: stats.totalSequences,
        completedSequences: stats.completedSequences
      }));

      // 按日统计
      const dailyStatsMap = new Map<string, { completedCount: number; totalTime: number; count: number }>();

      completedWithTime.forEach(sequence => {
        if (sequence.actualEndTime) {
          const date = sequence.actualEndTime.toISOString().substring(0, 10); // YYYY-MM-DD
          const duration = sequence.actualEndTime.getTime() - sequence.actualStartTime!.getTime();

          if (!dailyStatsMap.has(date)) {
            dailyStatsMap.set(date, { completedCount: 0, totalTime: 0, count: 0 });
          }

          const stats = dailyStatsMap.get(date)!;
          stats.completedCount++;
          stats.totalTime += duration;
          stats.count++;
        }
      });

      const dailyStats = Array.from(dailyStatsMap.entries()).map(([date, stats]) => ({
        date,
        completedCount: stats.completedCount,
        averageTime: stats.count > 0 ? Math.floor(stats.totalTime / stats.count / (1000 * 60)) : 0
      })).sort((a, b) => a.date.localeCompare(b.date));

      logger.info('获取生产统计数据成功', {
        enterpriseId,
        totalSequences,
        completedSequences,
        completionRate
      });

      return {
        totalSequences,
        completedSequences,
        inProgressSequences,
        waitingSequences,
        completionRate,
        averageProductionTime,
        deviceUtilization,
        dailyStats
      };
    } catch (error) {
      logger.error('获取生产统计数据失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }
}
