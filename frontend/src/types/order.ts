/**
 * 订单相关类型定义
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 18:45:00 +08:00; Reason: Shrimp Task ID: #435dc2b2-c806-4b50-be4a-1190e22a04c9, 创建前端订单类型定义; Principle_Applied: 类型安全;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 14:00:00 +08:00; Reason: Shrimp Task ID: #fa076766-4dc5-42e1-8100-77ad3fbeb5fa, 添加生产历史类型定义; Principle_Applied: 类型安全;}}
 */

// 订单状态枚举
export enum OrderStatus {
  UNSCHEDULED = 0,    // 未排产
  SCHEDULED = 1,      // 已排产
  IN_PRODUCTION = 2,  // 生产中
  COMPLETED = 3       // 已完成
}

// 订单类型枚举
export enum OrderType {
  DEFAULT = 1,        // 默认
  CUTTING = 2,        // 裁片
  HOME_TEXTILE = 3    // 家纺
}

// 设备订单排序状态枚举
export enum DeviceOrderSequenceStatus {
  WAITING = 0,      // 等待中
  IN_PRODUCTION = 1, // 生产中
  COMPLETED = 2     // 已完成
}

// 花样信息接口
export interface PatternInfo {
  patternId: string;
  patternQuantity: number;
}

// 订单基本信息接口
export interface Order {
  id: number;
  enterpriseId: number;
  type: OrderType;
  status: OrderStatus;
  code?: string;
  customerName?: string;
  salesman?: string;
  orderDate?: string;
  deliveryDate?: string;
  orderUnitId?: number;
  orderQuantity?: number;
  remark?: string;
  orderFiles?: string[];
  patternInfo?: PatternInfo[];
  createdAt: string;
  updatedAt: string;
  // 关联信息
  deviceOrderSequences?: DeviceOrderSequence[];
  enterprise?: {
    id: number;
    name: string;
  };
}

// 设备订单排序接口
export interface DeviceOrderSequence {
  id: number;
  enterpriseId: number;
  deviceId: number;
  orderId: number;
  patternId: number;
  formula?: string;
  lathesNum?: number;
  productionQuantity?: number;
  productionSequence?: number;
  status: DeviceOrderSequenceStatus;
  createdAt: string;
  updatedAt: string;
  // 关联信息
  device?: {
    id: number;
    name: string;
    code?: string;
    headNum?: number;
  };
  order?: {
    id: number;
    code?: string;
    customerName?: string;
    deliveryDate?: string;
  };
  pattern?: {
    id: number;
    name: string;
    code?: string;
  };
  enterprise?: {
    id: number;
    name: string;
  };
}

// 订单列表查询参数
export interface OrderListQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: OrderStatus;
  type?: OrderType;
  customerName?: string;
  salesman?: string;
  orderDateStart?: string;
  orderDateEnd?: string;
  deliveryDateStart?: string;
  deliveryDateEnd?: string;
}

// 订单列表响应
export interface OrderListResponse {
  orders: Order[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 搜索选项接口
export interface OrderSearchOptions {
  customers: Array<{ name: string; count: number }>;
  salesmen: Array<{ name: string; count: number }>;
  patterns: Array<{ id: number; name: string; code?: string }>;
  devices: Array<{ id: number; name: string; code?: string }>;
}

// 创建订单请求
export interface CreateOrderRequest {
  type?: OrderType;
  code?: string;
  customerName?: string;
  salesman?: string;
  orderDate?: string;
  deliveryDate?: string;
  orderUnitId?: number;
  orderQuantity?: number;
  remark?: string;
  orderFiles?: string[];
  patternInfo?: PatternInfo[];
}

// 更新订单请求
export interface UpdateOrderRequest {
  type?: OrderType;
  code?: string;
  customerName?: string;
  salesman?: string;
  orderDate?: string;
  deliveryDate?: string;
  orderUnitId?: number;
  orderQuantity?: number;
  remark?: string;
  orderFiles?: string[];
  patternInfo?: PatternInfo[];
}

// 订单状态更新请求
export interface UpdateOrderStatusRequest {
  status: OrderStatus;
  remark?: string;
}

// 设备排产列表查询参数
export interface SequenceListQuery {
  page?: number;
  pageSize?: number;
  deviceId?: number;
  orderId?: number;
  patternId?: number;
  status?: DeviceOrderSequenceStatus;
}

// 设备排产列表响应
export interface SequenceListResponse {
  sequences: DeviceOrderSequence[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 创建排产请求
export interface CreateSequenceRequest {
  deviceId: number;
  orderId: number;
  patternId: number;
  formula?: string;
  lathesNum?: number;
  productionSequence?: number;
}

// 更新排产请求
export interface UpdateSequenceRequest {
  formula?: string;
  lathesNum?: number;
  productionSequence?: number;
  status?: DeviceOrderSequenceStatus;
}

// 重新排序请求
export interface ReorderSequencesRequest {
  deviceId: number;
  sequenceIds: number[];
}

// 生产数量计算结果
export interface ProductionQuantityResult {
  productionQuantity: number;
  formula: string;
  lathesNum: number;
  deviceHeadNum: number;
}

// 生产数量计算请求
export interface CalculateProductionQuantityRequest {
  lathesNum: number;
  deviceId: number;
  formula: string;
}

// 订单状态选项
export const ORDER_STATUS_OPTIONS = [
  { value: OrderStatus.UNSCHEDULED, label: '未排产', color: '#d9d9d9' },
  { value: OrderStatus.SCHEDULED, label: '已排产', color: '#1890ff' },
  { value: OrderStatus.IN_PRODUCTION, label: '生产中', color: '#52c41a' },
  { value: OrderStatus.COMPLETED, label: '已完成', color: '#722ed1' }
];

// 订单类型选项
export const ORDER_TYPE_OPTIONS = [
  { value: OrderType.DEFAULT, label: '默认', color: '#1890ff' },
  { value: OrderType.CUTTING, label: '裁片', color: '#52c41a' },
  { value: OrderType.HOME_TEXTILE, label: '家纺', color: '#722ed1' }
];

// 设备排产状态选项
export const SEQUENCE_STATUS_OPTIONS = [
  { value: DeviceOrderSequenceStatus.WAITING, label: '等待中', color: '#d9d9d9' },
  { value: DeviceOrderSequenceStatus.IN_PRODUCTION, label: '生产中', color: '#52c41a' },
  { value: DeviceOrderSequenceStatus.COMPLETED, label: '已完成', color: '#722ed1' }
];

// 获取订单状态标签
export function getOrderStatusLabel(status: OrderStatus): string {
  const option = ORDER_STATUS_OPTIONS.find(item => item.value === status);
  return option?.label || '未知状态';
}

// 获取订单状态颜色
export function getOrderStatusColor(status: OrderStatus): string {
  const option = ORDER_STATUS_OPTIONS.find(item => item.value === status);
  return option?.color || '#d9d9d9';
}

// 获取订单类型标签
export function getOrderTypeLabel(type: OrderType): string {
  const option = ORDER_TYPE_OPTIONS.find(item => item.value === type);
  return option?.label || '未知类型';
}

// 获取订单类型颜色
export function getOrderTypeColor(type: OrderType): string {
  const option = ORDER_TYPE_OPTIONS.find(item => item.value === type);
  return option?.color || '#1890ff';
}

// 获取排产状态标签
export function getSequenceStatusLabel(status: DeviceOrderSequenceStatus): string {
  const option = SEQUENCE_STATUS_OPTIONS.find(item => item.value === status);
  return option?.label || '未知状态';
}

// 获取排产状态颜色
export function getSequenceStatusColor(status: DeviceOrderSequenceStatus): string {
  const option = SEQUENCE_STATUS_OPTIONS.find(item => item.value === status);
  return option?.color || '#d9d9d9';
}

// 检查订单是否可以编辑
export function canEditOrder(status: OrderStatus): boolean {
  return status === OrderStatus.UNSCHEDULED || status === OrderStatus.SCHEDULED;
}

// 检查订单是否可以删除
export function canDeleteOrder(status: OrderStatus): boolean {
  return status === OrderStatus.UNSCHEDULED;
}

// 检查排产是否可以编辑
export function canEditSequence(status: DeviceOrderSequenceStatus): boolean {
  return status === DeviceOrderSequenceStatus.WAITING;
}

// 检查排产是否可以删除
export function canDeleteSequence(status: DeviceOrderSequenceStatus): boolean {
  return status === DeviceOrderSequenceStatus.WAITING;
}

// 检查排产是否可以开始生产
export function canStartProduction(status: DeviceOrderSequenceStatus): boolean {
  return status === DeviceOrderSequenceStatus.WAITING;
}

// 检查排产是否可以完成生产
export function canCompleteProduction(status: DeviceOrderSequenceStatus): boolean {
  return status === DeviceOrderSequenceStatus.IN_PRODUCTION;
}

// 计算订单总量
export function calculateOrderQuantity(patternInfo?: PatternInfo[]): number {
  if (!patternInfo || patternInfo.length === 0) return 0;
  return patternInfo.reduce((sum, item) => sum + item.patternQuantity, 0);
}

// 格式化日期显示
export function formatOrderDate(dateString?: string): string {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}

// 格式化日期时间显示
export function formatOrderDateTime(dateString?: string): string {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 计算订单延期天数
export function calculateDelayDays(deliveryDate?: string): number {
  if (!deliveryDate) return 0;

  const delivery = new Date(deliveryDate);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  delivery.setHours(0, 0, 0, 0);

  const diffTime = today.getTime() - delivery.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays > 0 ? diffDays : 0;
}

// 检查订单是否延期
export function isOrderDelayed(deliveryDate?: string): boolean {
  return calculateDelayDays(deliveryDate) > 0;
}

// 生产历史操作类型枚举
export enum ProductionHistoryActionType {
  START = 'START',           // 开始生产
  PAUSE = 'PAUSE',           // 暂停生产
  RESUME = 'RESUME',         // 恢复生产
  COMPLETE = 'COMPLETE',     // 完成生产
  CANCEL = 'CANCEL',         // 取消生产
  ERROR = 'ERROR',           // 生产异常
  PROGRESS_UPDATE = 'PROGRESS_UPDATE', // 进度更新
  QUANTITY_UPDATE = 'QUANTITY_UPDATE'  // 数量更新
}

// 生产历史记录接口
export interface ProductionHistory {
  id: number;
  enterpriseId: number;
  sequenceId: number;
  actionType: ProductionHistoryActionType;
  actionTime: string;
  operatorId?: number;
  previousValue?: string;
  newValue?: string;
  remark?: string;
  createdAt: string;
  updatedAt: string;
  // 关联数据
  sequence?: DeviceOrderSequence;
  operator?: {
    id: number;
    realName?: string;
    username: string;
  };
}

// 拆分分配项接口
export interface SplitAllocation {
  key?: number;
  deviceId: number;
  patternId?: number; // 改为可选，避免显示0
  lathesNum: number;
  formula: string;
  productionQuantity: number;
}

// 批量创建排产请求接口
export interface BatchCreateSequenceRequest {
  orderId: number;
  allocations: Array<{
    deviceId: number;
    patternId: number;
    lathesNum: number;
    formula?: string;
  }>;
}

// 订单拆分表单数据接口
export interface OrderSplitFormData {
  orderId: number;
  allocations: SplitAllocation[];
}
